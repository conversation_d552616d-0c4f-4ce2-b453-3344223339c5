"use client";

import { QueryProvider } from "@/lib/providers/QueryProvider";
import { AuthProvider, useAuthContext } from "@/lib/providers/AuthProvider";
import { ErrorBoundary } from "@/components/shared/ErrorBoundary";
import { ErrorFallback } from "@/components/shared/ErrorFallback";
import { OnboardingModal } from "@/components/auth/OnboardingModal";
import { ReactNode } from "react";
import { usePrefetchCriticalRoutes, usePrefetchSecondaryRoutes } from "@/lib/utils/navigation";

interface ClientLayoutProps {
  children: ReactNode;
}

// Component that renders onboarding modal when needed
function OnboardingWrapper({ children }: { children: ReactNode }) {
  const {
    user,
    needsOnboarding,
    completeOnboarding,
    isCompleting,
    onboardingError
  } = useAuthContext();

  return (
    <>
      {children}
      {/* Render onboarding modal when user needs onboarding */}
      {user && needsOnboarding && (
        <OnboardingModal
          isOpen={needsOnboarding}
          onComplete={completeOnboarding}
          isCompleting={isCompleting}
          error={onboardingError}
        />
      )}
    </>
  );
}

export function ClientLayout({ children }: ClientLayoutProps) {
  // Prefetch critical routes for better navigation performance
  usePrefetchCriticalRoutes();
  usePrefetchSecondaryRoutes();

  return (
    <ErrorBoundary
      level="page"
      fallback={<ErrorFallback variant="fullscreen" />}
      context={{ component: 'RootLayout' }}
    >
      <QueryProvider>
        <AuthProvider>
          <OnboardingWrapper>
            {children}
          </OnboardingWrapper>
        </AuthProvider>
      </QueryProvider>
    </ErrorBoundary>
  );
}
